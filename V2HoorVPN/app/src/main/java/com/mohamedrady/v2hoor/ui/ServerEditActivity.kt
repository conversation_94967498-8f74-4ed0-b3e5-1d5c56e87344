package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import android.view.MenuItem
import androidx.appcompat.app.AppCompatActivity
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.util.toast

/**
 * Server Edit Activity for Firebase servers
 * TODO: Implement full server editing functionality
 */
class ServerEditActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_server_edit)
        
        setupToolbar()
        handleIntent()
    }

    private fun setupToolbar() {
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.server_edit_title)
        }
    }

    private fun handleIntent() {
        val serverId = intent.getStringExtra("server_id")
        val serverName = intent.getStringExtra("server_name")
        val serverConfigType = intent.getStringExtra("server_config_type")
        
        if (serverId != null) {
            toast("تعديل السيرفر: $serverName")
            // TODO: Load server data and populate form
        } else {
            toast("إضافة سيرفر جديد")
            // TODO: Setup form for new server
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}
