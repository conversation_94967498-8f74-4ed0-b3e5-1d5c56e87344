package com.mohamedrady.v2hoor.util

import android.content.Context
import android.widget.Toast
import androidx.annotation.StringRes

/**
 * Toast utility functions for V2Hoor VPN
 */

fun Context.toast(message: String, duration: Int = Toast.LENGTH_SHORT) {
    Toast.makeText(this, message, duration).show()
}

fun Context.toast(@StringRes messageRes: Int, duration: Int = Toast.LENGTH_SHORT) {
    Toast.makeText(this, messageRes, duration).show()
}

fun Context.toastLong(message: String) {
    Toast.makeText(this, message, Toast.LENGTH_LONG).show()
}

fun Context.toastLong(@StringRes messageRes: Int) {
    Toast.makeText(this, messageRes, Toast.LENGTH_LONG).show()
}

fun Context.toastSuccess(message: String) {
    Toast.makeText(this, "✅ $message", Toast.LENGTH_SHORT).show()
}

fun Context.toastError(message: String) {
    Toast.makeText(this, "❌ $message", Toast.LENGTH_LONG).show()
}

fun Context.toastWarning(message: String) {
    Toast.makeText(this, "⚠️ $message", Toast.LENGTH_SHORT).show()
}

fun Context.toastInfo(message: String) {
    Toast.makeText(this, "ℹ️ $message", Toast.LENGTH_SHORT).show()
}
