package com.mohamedrady.v2hoor.ui

import android.annotation.SuppressLint
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.tabs.TabLayout
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityServersManagementBinding
import com.mohamedrady.v2hoor.dto.EConfigType
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.handler.AngConfigManager
import com.mohamedrady.v2hoor.handler.MmkvManager
import com.mohamedrady.v2hoor.util.Utils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class ServersManagementActivity : BaseActivity() {
    private val binding by lazy { ActivityServersManagementBinding.inflate(layoutInflater) }

    private val adapter by lazy { ServersManagementAdapter(this) }
    
    // Activity result launchers
    private val scanQRCodeForConfig = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        if (it.resultCode == RESULT_OK) {
            importBatchConfig(it.data?.getStringExtra("SCAN_RESULT"))
        }
    }
    
    private val chooseFileForConfig = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        val uri = it.data?.data
        if (it.resultCode == RESULT_OK && uri != null) {
            readContentFromUri(uri)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        
        title = getString(R.string.title_servers_management)
        setSupportActionBar(binding.toolbar)
        
        setupRecyclerView()
        setupTabs()
        setupFab()
        loadServers()
    }
    
    private fun setupRecyclerView() {
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter
    }
    
    private fun setupTabs() {
        binding.tabGroup.addTab(binding.tabGroup.newTab().setText(getString(R.string.tab_all_servers)))
        binding.tabGroup.addTab(binding.tabGroup.newTab().setText("VMESS"))
        binding.tabGroup.addTab(binding.tabGroup.newTab().setText("VLESS"))
        binding.tabGroup.addTab(binding.tabGroup.newTab().setText("Shadowsocks"))
        binding.tabGroup.addTab(binding.tabGroup.newTab().setText("Trojan"))
        binding.tabGroup.addTab(binding.tabGroup.newTab().setText("WireGuard"))
        
        binding.tabGroup.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                filterServersByType(tab?.position ?: 0)
            }
            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
    }
    
    private fun setupFab() {
        binding.fab.setOnClickListener {
            showAddServerDialog()
        }
    }
    
    private fun showAddServerDialog() {
        val options = arrayOf(
            getString(R.string.import_from_clipboard),
            getString(R.string.import_from_qr_code),
            getString(R.string.import_from_file),
            getString(R.string.add_vmess_server),
            getString(R.string.add_vless_server),
            getString(R.string.add_shadowsocks_server),
            getString(R.string.add_trojan_server),
            getString(R.string.add_wireguard_server)
        )
        
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.add_server))
            .setItems(options) { _, which ->
                when (which) {
                    0 -> importFromClipboard()
                    1 -> importFromQRCode()
                    2 -> importFromFile()
                    3 -> addManualServer(EConfigType.VMESS)
                    4 -> addManualServer(EConfigType.VLESS)
                    5 -> addManualServer(EConfigType.SHADOWSOCKS)
                    6 -> addManualServer(EConfigType.TROJAN)
                    7 -> addManualServer(EConfigType.WIREGUARD)
                }
            }
            .show()
    }
    
    private fun importFromClipboard() {
        val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clipData = clipboard.primaryClip
        if (clipData != null && clipData.itemCount > 0) {
            val text = clipData.getItemAt(0).text?.toString()
            if (!text.isNullOrEmpty()) {
                importBatchConfig(text)
            } else {
                toast(getString(R.string.clipboard_empty))
            }
        } else {
            toast(getString(R.string.clipboard_empty))
        }
    }
    
    private fun importFromQRCode() {
        scanQRCodeForConfig.launch(Intent(this, ScannerActivity::class.java))
    }
    
    private fun importFromFile() {
        val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
            type = "*/*"
            addCategory(Intent.CATEGORY_OPENABLE)
        }
        chooseFileForConfig.launch(intent)
    }
    
    private fun addManualServer(configType: EConfigType) {
        startActivity(Intent(this, ServerActivity::class.java).apply {
            putExtra("createConfigType", configType.value)
        })
    }
    
    private fun importBatchConfig(content: String?) {
        if (content.isNullOrEmpty()) {
            toast(getString(R.string.toast_none_data))
            return
        }
        
        binding.pbWaiting.visibility = View.VISIBLE
        
        lifecycleScope.launch(Dispatchers.IO) {
            val (count, countSub) = AngConfigManager.importBatchConfig(content, "", false)

            launch(Dispatchers.Main) {
                binding.pbWaiting.visibility = View.GONE
                if (count > 0 || countSub > 0) {
                    toastSuccess(getString(R.string.toast_success))
                    loadServers()
                } else {
                    toastError(getString(R.string.toast_failure))
                }
            }
        }
    }
    
    private fun readContentFromUri(uri: Uri) {
        try {
            contentResolver.openInputStream(uri)?.use { input ->
                val content = input.bufferedReader().readText()
                importBatchConfig(content)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            toast(getString(R.string.toast_failure))
        }
    }
    
    @SuppressLint("NotifyDataSetChanged")
    private fun loadServers() {
        val serverGuids = MmkvManager.decodeServerList()
        adapter.updateServers(serverGuids)
        adapter.notifyDataSetChanged()
    }

    private fun filterServersByType(tabPosition: Int) {
        val allServers = MmkvManager.decodeServerList()
        val filteredServers = when (tabPosition) {
            0 -> allServers // All servers
            1 -> allServers.filter { guid ->
                MmkvManager.decodeServerConfig(guid)?.configType == EConfigType.VMESS
            }
            2 -> allServers.filter { guid ->
                MmkvManager.decodeServerConfig(guid)?.configType == EConfigType.VLESS
            }
            3 -> allServers.filter { guid ->
                MmkvManager.decodeServerConfig(guid)?.configType == EConfigType.SHADOWSOCKS
            }
            4 -> allServers.filter { guid ->
                MmkvManager.decodeServerConfig(guid)?.configType == EConfigType.TROJAN
            }
            5 -> allServers.filter { guid ->
                MmkvManager.decodeServerConfig(guid)?.configType == EConfigType.WIREGUARD
            }
            else -> allServers
        }

        adapter.updateServers(filteredServers)
    }
    
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_servers_management, menu)
        return super.onCreateOptionsMenu(menu)
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_test_all -> {
                testAllServers()
                true
            }
            R.id.action_export_all -> {
                exportAllServers()
                true
            }
            R.id.action_delete_all -> {
                deleteAllServers()
                true
            }
            R.id.action_delete_duplicates -> {
                deleteDuplicateServers()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    private fun testAllServers() {
        binding.pbWaiting.visibility = View.VISIBLE
        toast(getString(R.string.connection_test_testing))
        
        lifecycleScope.launch(Dispatchers.IO) {
            // Test all servers logic here
            launch(Dispatchers.Main) {
                binding.pbWaiting.visibility = View.GONE
                toast(getString(R.string.connection_test_completed))
                loadServers()
            }
        }
    }
    
    private fun exportAllServers() {
        lifecycleScope.launch(Dispatchers.IO) {
            val serverGuids = MmkvManager.decodeServerList()
            val result = AngConfigManager.shareNonCustomConfigsToClipboard(this@ServersManagementActivity, serverGuids)
            launch(Dispatchers.Main) {
                if (result > 0) {
                    toastSuccess(getString(R.string.toast_success))
                } else {
                    toastError(getString(R.string.toast_failure))
                }
            }
        }
    }
    
    private fun deleteAllServers() {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.del_config_comfirm))
            .setMessage(getString(R.string.del_all_config_comfirm))
            .setPositiveButton(android.R.string.ok) { _, _ ->
                MmkvManager.removeAllServer()
                loadServers()
                toast(getString(R.string.toast_success))
            }
            .setNegativeButton(android.R.string.cancel, null)
            .show()
    }
    
    private fun deleteDuplicateServers() {
        lifecycleScope.launch(Dispatchers.IO) {
            // Simple duplicate removal logic
            val serverGuids = MmkvManager.decodeServerList()
            val uniqueServers = mutableSetOf<String>()
            var removedCount = 0

            serverGuids.forEach { guid ->
                val config = MmkvManager.decodeServerConfig(guid)
                if (config != null) {
                    val serverKey = "${config.server}:${config.serverPort}"
                    if (!uniqueServers.add(serverKey)) {
                        MmkvManager.removeServer(guid)
                        removedCount++
                    }
                }
            }

            launch(Dispatchers.Main) {
                if (removedCount > 0) {
                    toastSuccess(getString(R.string.toast_success))
                    loadServers()
                } else {
                    toast(getString(R.string.no_duplicate_servers))
                }
            }
        }
    }
    
    override fun onResume() {
        super.onResume()
        loadServers()
    }
}
