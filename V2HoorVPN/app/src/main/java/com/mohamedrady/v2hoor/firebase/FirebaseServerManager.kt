package com.mohamedrady.v2hoor.firebase

import android.util.Log
import com.google.firebase.database.*
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.dto.ProfileItem
import com.mohamedrady.v2hoor.handler.MmkvManager
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await

/**
 * Firebase Server Manager for handling server operations
 */
object FirebaseServerManager {
    private const val TAG = "FirebaseServerManager"
    private const val SERVERS_PATH = "servers"
    private const val CATEGORIES_PATH = "categories"
    private const val STATS_PATH = "server_stats"

    private val database: DatabaseReference by lazy {
        try {
            Log.d(TAG, "Initializing Firebase Database connection...")
            val db = FirebaseDatabase.getInstance("https://mrelfeky-209615-default-rtdb.firebaseio.com/")
            Log.d(TAG, "Firebase Database instance created successfully")
            db.reference
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Firebase Database", e)
            throw e
        }
    }
    
    private val serversRef: DatabaseReference by lazy {
        database.child(SERVERS_PATH)
    }
    
    private val categoriesRef: DatabaseReference by lazy {
        database.child(CATEGORIES_PATH)
    }
    
    private val statsRef: DatabaseReference by lazy {
        database.child(STATS_PATH)
    }

    /**
     * Test Firebase connection
     */
    suspend fun testConnection(): Boolean {
        return try {
            Log.d(TAG, "Testing Firebase connection...")
            val testRef = database.child("test")
            testRef.setValue("connection_test_${System.currentTimeMillis()}").await()
            Log.d(TAG, "Firebase connection test successful")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Firebase connection test failed", e)
            false
        }
    }

    /**
     * Add sample data for testing
     */
    suspend fun addSampleData(): Boolean {
        return try {
            Log.d(TAG, "Adding sample data to Firebase...")

            // Add sample categories
            val categories = listOf(
                FirebaseServerCategory(
                    id = "premium",
                    name = "بريميوم",
                    description = "سيرفرات عالية السرعة",
                    color = "#4CAF50",
                    sortOrder = 1
                ),
                FirebaseServerCategory(
                    id = "free",
                    name = "مجاني",
                    description = "سيرفرات مجانية",
                    color = "#2196F3",
                    sortOrder = 2
                )
            )

            categories.forEach { category ->
                categoriesRef.child(category.id).setValue(category).await()
            }

            // Add sample servers
            val servers = listOf(
                FirebaseServerModel(
                    id = "server1",
                    name = "سيرفر أمريكا",
                    description = "سيرفر عالي السرعة في أمريكا",
                    configType = "VMESS",
                    server = "us.example.com",
                    serverPort = "443",
                    category = "premium",
                    country = "الولايات المتحدة",
                    city = "نيويورك",
                    provider = "CloudFlare",
                    speed = "100MB/s",
                    ping = 50,
                    sortOrder = 1
                ),
                FirebaseServerModel(
                    id = "server2",
                    name = "سيرفر ألمانيا",
                    description = "سيرفر مجاني في ألمانيا",
                    configType = "VLESS",
                    server = "de.example.com",
                    serverPort = "80",
                    category = "free",
                    country = "ألمانيا",
                    city = "برلين",
                    provider = "Hetzner",
                    speed = "50MB/s",
                    ping = 80,
                    sortOrder = 2
                )
            )

            servers.forEach { server ->
                serversRef.child(server.id).setValue(server).await()
            }

            Log.d(TAG, "Sample data added successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to add sample data", e)
            false
        }
    }

    /**
     * Get all servers from Firebase
     */
    suspend fun getAllServers(): List<FirebaseServerModel> {
        return try {
            val snapshot = serversRef.get().await()
            val servers = mutableListOf<FirebaseServerModel>()
            
            snapshot.children.forEach { child ->
                child.getValue(FirebaseServerModel::class.java)?.let { server ->
                    servers.add(server)
                }
            }
            
            Log.d(TAG, "Retrieved ${servers.size} servers from Firebase")
            servers.sortedBy { it.sortOrder }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting servers from Firebase", e)
            emptyList()
        }
    }

    /**
     * Get servers by category
     */
    suspend fun getServersByCategory(category: String): List<FirebaseServerModel> {
        return try {
            val snapshot = serversRef.orderByChild("category").equalTo(category).get().await()
            val servers = mutableListOf<FirebaseServerModel>()
            
            snapshot.children.forEach { child ->
                child.getValue(FirebaseServerModel::class.java)?.let { server ->
                    if (server.isActive) {
                        servers.add(server)
                    }
                }
            }
            
            servers.sortedBy { it.sortOrder }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting servers by category from Firebase", e)
            emptyList()
        }
    }

    /**
     * Add server to Firebase
     */
    suspend fun addServer(server: FirebaseServerModel): Boolean {
        return try {
            val serverId = if (server.id.isEmpty()) {
                serversRef.push().key ?: return false
            } else {
                server.id
            }
            
            val serverWithId = server.copy(
                id = serverId,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )
            
            serversRef.child(serverId).setValue(serverWithId).await()
            Log.d(TAG, "Server added to Firebase: $serverId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error adding server to Firebase", e)
            false
        }
    }

    /**
     * Update server in Firebase
     */
    suspend fun updateServer(server: FirebaseServerModel): Boolean {
        return try {
            if (server.id.isEmpty()) return false
            
            val updatedServer = server.copy(updatedAt = System.currentTimeMillis())
            serversRef.child(server.id).setValue(updatedServer).await()
            Log.d(TAG, "Server updated in Firebase: ${server.id}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating server in Firebase", e)
            false
        }
    }

    /**
     * Delete server from Firebase
     */
    suspend fun deleteServer(serverId: String): Boolean {
        return try {
            serversRef.child(serverId).removeValue().await()
            statsRef.child(serverId).removeValue().await()
            Log.d(TAG, "Server deleted from Firebase: $serverId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting server from Firebase", e)
            false
        }
    }

    /**
     * Sync local servers to Firebase
     */
    suspend fun syncLocalServersToFirebase(): Boolean {
        return try {
            val localServerGuids = MmkvManager.decodeServerList()
            var syncedCount = 0
            
            localServerGuids.forEach { guid ->
                MmkvManager.decodeServerConfig(guid)?.let { profileItem ->
                    val firebaseServer = FirebaseServerModel.fromProfileItem(profileItem)
                    firebaseServer.id = guid
                    firebaseServer.isReadOnly = false // Local servers are editable
                    
                    if (addServer(firebaseServer)) {
                        syncedCount++
                    }
                }
            }
            
            Log.d(TAG, "Synced $syncedCount local servers to Firebase")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error syncing local servers to Firebase", e)
            false
        }
    }

    /**
     * Sync Firebase servers to local storage
     */
    suspend fun syncFirebaseServersToLocal(): Boolean {
        return try {
            val firebaseServers = getAllServers()
            var syncedCount = 0
            
            firebaseServers.forEach { firebaseServer ->
                val profileItem = firebaseServer.toProfileItem()
                profileItem.subscriptionId = "firebase_${firebaseServer.category}"
                
                val guid = MmkvManager.encodeServerConfig(firebaseServer.id, profileItem)
                if (guid.isNotEmpty()) {
                    syncedCount++
                }
            }
            
            Log.d(TAG, "Synced $syncedCount Firebase servers to local storage")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error syncing Firebase servers to local", e)
            false
        }
    }

    /**
     * Listen to server changes in real-time
     */
    fun listenToServerChanges(): Flow<List<FirebaseServerModel>> = callbackFlow {
        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                val servers = mutableListOf<FirebaseServerModel>()
                
                snapshot.children.forEach { child ->
                    child.getValue(FirebaseServerModel::class.java)?.let { server ->
                        if (server.isActive) {
                            servers.add(server)
                        }
                    }
                }
                
                trySend(servers.sortedBy { it.sortOrder })
            }

            override fun onCancelled(error: DatabaseError) {
                Log.e(TAG, "Error listening to server changes", error.toException())
                close(error.toException())
            }
        }
        
        serversRef.addValueEventListener(listener)
        
        awaitClose {
            serversRef.removeEventListener(listener)
        }
    }

    /**
     * Get all categories
     */
    suspend fun getAllCategories(): List<FirebaseServerCategory> {
        return try {
            val snapshot = categoriesRef.get().await()
            val categories = mutableListOf<FirebaseServerCategory>()
            
            snapshot.children.forEach { child ->
                child.getValue(FirebaseServerCategory::class.java)?.let { category ->
                    if (category.isActive) {
                        categories.add(category)
                    }
                }
            }
            
            categories.sortedBy { it.sortOrder }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting categories from Firebase", e)
            emptyList()
        }
    }

    /**
     * Add category to Firebase
     */
    suspend fun addCategory(category: FirebaseServerCategory): Boolean {
        return try {
            val categoryId = if (category.id.isEmpty()) {
                categoriesRef.push().key ?: return false
            } else {
                category.id
            }
            
            val categoryWithId = category.copy(
                id = categoryId,
                createdAt = System.currentTimeMillis()
            )
            
            categoriesRef.child(categoryId).setValue(categoryWithId).await()
            Log.d(TAG, "Category added to Firebase: $categoryId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error adding category to Firebase", e)
            false
        }
    }

    /**
     * Update server statistics
     */
    suspend fun updateServerStats(serverId: String, stats: FirebaseServerStats): Boolean {
        return try {
            val updatedStats = stats.copy(
                serverId = serverId,
                updatedAt = System.currentTimeMillis()
            )
            
            statsRef.child(serverId).setValue(updatedStats).await()
            Log.d(TAG, "Server stats updated: $serverId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating server stats", e)
            false
        }
    }

    /**
     * Get server statistics
     */
    suspend fun getServerStats(serverId: String): FirebaseServerStats? {
        return try {
            val snapshot = statsRef.child(serverId).get().await()
            snapshot.getValue(FirebaseServerStats::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting server stats", e)
            null
        }
    }
}
