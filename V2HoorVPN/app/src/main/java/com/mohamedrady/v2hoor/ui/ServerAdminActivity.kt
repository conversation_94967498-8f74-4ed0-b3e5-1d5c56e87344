package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityServerAdminBinding
import com.mohamedrady.v2hoor.firebase.FirebaseServerManager
import com.mohamedrady.v2hoor.firebase.FirebaseServerModel
import com.mohamedrady.v2hoor.firebase.FirebaseServerCategory
import com.mohamedrady.v2hoor.ui.adapter.ServerAdminAdapter
import com.mohamedrady.v2hoor.util.toast
import com.mohamedrady.v2hoor.util.toastError
import com.mohamedrady.v2hoor.util.toastSuccess
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Server Admin Activity for managing Firebase servers
 */
class ServerAdminActivity : BaseActivity() {
    private val binding by lazy { ActivityServerAdminBinding.inflate(layoutInflater) }
    private val adapter by lazy { ServerAdminAdapter(this) }
    
    private var currentCategory = "all"
    private var categories = listOf<FirebaseServerCategory>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        
        setupToolbar()
        setupRecyclerView()
        setupListeners()
        loadData()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.server_admin_title)
        }
    }

    private fun setupRecyclerView() {
        binding.recyclerView.apply {
            layoutManager = LinearLayoutManager(this@ServerAdminActivity)
            adapter = <EMAIL>
        }
    }

    private fun setupListeners() {
        binding.fabAddServer.setOnClickListener {
            startActivity(Intent(this, ServerEditActivity::class.java))
        }
        
        binding.swipeRefresh.setOnRefreshListener {
            loadServers()
        }
        
        binding.btnSyncToLocal.setOnClickListener {
            syncFirebaseToLocal()
        }
        
        binding.btnSyncToFirebase.setOnClickListener {
            syncLocalToFirebase()
        }

        binding.btnTestConnection.setOnClickListener {
            testFirebaseConnection()
        }

        binding.btnAddSampleData.setOnClickListener {
            addSampleData()
        }
        
        // Category filter
        binding.chipGroupCategories.setOnCheckedStateChangeListener { _, checkedIds ->
            if (checkedIds.isNotEmpty()) {
                val selectedChip = findViewById<com.google.android.material.chip.Chip>(checkedIds[0])
                currentCategory = selectedChip?.tag as? String ?: "all"
                loadServers()
            }
        }
    }

    private fun loadData() {
        loadCategories()
        loadServers()
    }

    private fun loadCategories() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                categories = FirebaseServerManager.getAllCategories()
                
                launch(Dispatchers.Main) {
                    setupCategoryChips()
                }
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    toastError("خطأ في تحميل الفئات: ${e.message}")
                }
            }
        }
    }

    private fun setupCategoryChips() {
        binding.chipGroupCategories.removeAllViews()
        
        // Add "All" chip
        val allChip = com.google.android.material.chip.Chip(this).apply {
            text = "الكل"
            tag = "all"
            isCheckable = true
            isChecked = currentCategory == "all"
        }
        binding.chipGroupCategories.addView(allChip)
        
        // Add category chips
        categories.forEach { category ->
            val chip = com.google.android.material.chip.Chip(this).apply {
                text = category.name
                tag = category.id
                isCheckable = true
                isChecked = currentCategory == category.id
            }
            binding.chipGroupCategories.addView(chip)
        }
    }

    private fun loadServers() {
        binding.progressBar.visibility = View.VISIBLE
        
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val servers = if (currentCategory == "all") {
                    FirebaseServerManager.getAllServers()
                } else {
                    FirebaseServerManager.getServersByCategory(currentCategory)
                }
                
                launch(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    binding.swipeRefresh.isRefreshing = false
                    
                    adapter.updateServers(servers)
                    updateEmptyState(servers.isEmpty())
                    
                    binding.tvServerCount.text = "عدد السيرفرات: ${servers.size}"
                }
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    binding.swipeRefresh.isRefreshing = false
                    toastError("خطأ في تحميل السيرفرات: ${e.message}")
                }
            }
        }
    }

    private fun updateEmptyState(isEmpty: Boolean) {
        binding.layoutEmptyState.visibility = if (isEmpty) View.VISIBLE else View.GONE
        binding.recyclerView.visibility = if (isEmpty) View.GONE else View.VISIBLE
    }

    private fun syncFirebaseToLocal() {
        binding.progressBar.visibility = View.VISIBLE
        toast("جاري مزامنة السيرفرات من Firebase...")
        
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val success = FirebaseServerManager.syncFirebaseServersToLocal()
                
                launch(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    
                    if (success) {
                        toastSuccess("تم مزامنة السيرفرات بنجاح")
                    } else {
                        toastError("فشل في مزامنة السيرفرات")
                    }
                }
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    toastError("خطأ في المزامنة: ${e.message}")
                }
            }
        }
    }

    private fun syncLocalToFirebase() {
        binding.progressBar.visibility = View.VISIBLE
        toast("جاري مزامنة السيرفرات إلى Firebase...")

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val success = FirebaseServerManager.syncLocalServersToFirebase()

                launch(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE

                    if (success) {
                        toastSuccess("تم رفع السيرفرات بنجاح")
                        loadServers() // Reload to show updated data
                    } else {
                        toastError("فشل في رفع السيرفرات")
                    }
                }
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    toastError("خطأ في الرفع: ${e.message}")
                }
            }
        }
    }

    private fun testFirebaseConnection() {
        binding.progressBar.visibility = View.VISIBLE
        toast("جاري اختبار الاتصال بـ Firebase...")

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val success = FirebaseServerManager.testConnection()

                launch(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE

                    if (success) {
                        toastSuccess("تم الاتصال بـ Firebase بنجاح!")
                    } else {
                        toastError("فشل في الاتصال بـ Firebase")
                    }
                }
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    toastError("خطأ في الاتصال: ${e.message}")
                }
            }
        }
    }

    private fun addSampleData() {
        binding.progressBar.visibility = View.VISIBLE
        toast("جاري إضافة بيانات تجريبية...")

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val success = FirebaseServerManager.addSampleData()

                launch(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE

                    if (success) {
                        toastSuccess("تم إضافة البيانات التجريبية بنجاح!")
                        loadData() // Reload data to show new servers
                    } else {
                        toastError("فشل في إضافة البيانات التجريبية")
                    }
                }
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    toastError("خطأ في إضافة البيانات: ${e.message}")
                }
            }
        }
    }

    fun editServer(server: FirebaseServerModel) {
        val intent = Intent(this, ServerEditActivity::class.java).apply {
            putExtra("server_id", server.id)
            putExtra("server_name", server.name)
            putExtra("server_config_type", server.configType)
        }
        startActivity(intent)
    }

    fun deleteServer(server: FirebaseServerModel) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("حذف السيرفر")
            .setMessage("هل أنت متأكد من حذف السيرفر '${server.name}'؟")
            .setPositiveButton("حذف") { _, _ ->
                performDeleteServer(server)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun performDeleteServer(server: FirebaseServerModel) {
        binding.progressBar.visibility = View.VISIBLE
        
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val success = FirebaseServerManager.deleteServer(server.id)
                
                launch(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    
                    if (success) {
                        toastSuccess("تم حذف السيرفر بنجاح")
                        loadServers()
                    } else {
                        toastError("فشل في حذف السيرفر")
                    }
                }
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    toastError("خطأ في الحذف: ${e.message}")
                }
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_server_admin, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            R.id.action_add_category -> {
                // TODO: Implement add category dialog
                toast("إضافة فئة جديدة")
                true
            }
            R.id.action_settings -> {
                // TODO: Implement admin settings
                toast("إعدادات الإدارة")
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onResume() {
        super.onResume()
        loadServers() // Refresh data when returning from edit activity
    }
}
