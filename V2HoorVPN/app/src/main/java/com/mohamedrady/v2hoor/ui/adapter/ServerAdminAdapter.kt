package com.mohamedrady.v2hoor.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ItemServerAdminBinding
import com.mohamedrady.v2hoor.firebase.FirebaseServerModel
import com.mohamedrady.v2hoor.ui.ServerAdminActivity
import java.text.SimpleDateFormat
import java.util.*

/**
 * Adapter for Server Admin RecyclerView
 */
class ServerAdminAdapter(
    private val activity: ServerAdminActivity
) : RecyclerView.Adapter<ServerAdminAdapter.ServerAdminViewHolder>() {

    private var servers = listOf<FirebaseServerModel>()
    private val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())

    fun updateServers(newServers: List<FirebaseServerModel>) {
        servers = newServers
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ServerAdminViewHolder {
        val binding = ItemServerAdminBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return ServerAdminViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ServerAdminViewHolder, position: Int) {
        holder.bind(servers[position])
    }

    override fun getItemCount(): Int = servers.size

    inner class ServerAdminViewHolder(
        private val binding: ItemServerAdminBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(server: FirebaseServerModel) {
            binding.apply {
                // Server basic info
                tvServerName.text = server.name.ifEmpty { "سيرفر ${adapterPosition + 1}" }
                tvServerAddress.text = "${server.server}:${server.serverPort}"
                tvServerType.text = server.configType
                tvServerCategory.text = server.category.ifEmpty { "افتراضي" }
                
                // Server details
                tvServerDescription.text = server.description.ifEmpty { "لا يوجد وصف" }
                tvServerCountry.text = server.country.ifEmpty { "غير محدد" }
                tvServerProvider.text = server.provider.ifEmpty { "غير محدد" }
                
                // Server status
                setServerStatus(server)
                
                // Timestamps
                tvCreatedAt.text = "تم الإنشاء: ${dateFormat.format(Date(server.createdAt))}"
                tvUpdatedAt.text = "آخر تحديث: ${dateFormat.format(Date(server.updatedAt))}"
                
                // Server type icon
                setServerTypeIcon(server.configType)
                
                // Read-only indicator
                if (server.isReadOnly) {
                    chipReadOnly.visibility = android.view.View.VISIBLE
                    btnEdit.isEnabled = false
                    btnEdit.alpha = 0.5f
                } else {
                    chipReadOnly.visibility = android.view.View.GONE
                    btnEdit.isEnabled = true
                    btnEdit.alpha = 1.0f
                }
                
                // Click listeners
                btnEdit.setOnClickListener {
                    if (!server.isReadOnly) {
                        activity.editServer(server)
                    }
                }
                
                btnDelete.setOnClickListener {
                    activity.deleteServer(server)
                }
                
                cardServer.setOnClickListener {
                    toggleServerDetails()
                }
                
                // Server statistics (if available)
                if (server.ping > 0) {
                    tvPing.text = "${server.ping}ms"
                    tvPing.visibility = android.view.View.VISIBLE
                } else {
                    tvPing.visibility = android.view.View.GONE
                }
                
                if (server.speed.isNotEmpty()) {
                    tvSpeed.text = server.speed
                    tvSpeed.visibility = android.view.View.VISIBLE
                } else {
                    tvSpeed.visibility = android.view.View.GONE
                }
            }
        }
        
        private fun setServerStatus(server: FirebaseServerModel) {
            binding.apply {
                if (server.isActive) {
                    chipStatus.text = "نشط"
                    chipStatus.setChipBackgroundColorResource(R.color.color_success)
                    chipStatus.setTextColor(ContextCompat.getColor(itemView.context, android.R.color.white))
                } else {
                    chipStatus.text = "غير نشط"
                    chipStatus.setChipBackgroundColorResource(R.color.color_error)
                    chipStatus.setTextColor(ContextCompat.getColor(itemView.context, android.R.color.white))
                }
            }
        }
        
        private fun setServerTypeIcon(configType: String) {
            val iconRes = when (configType.uppercase()) {
                "VMESS" -> R.drawable.ic_vmess_24dp
                "VLESS" -> R.drawable.ic_vless_24dp
                "SHADOWSOCKS" -> R.drawable.ic_shadowsocks_24dp
                "TROJAN" -> R.drawable.ic_trojan_24dp
                "SOCKS" -> R.drawable.ic_socks_24dp
                "HTTP" -> R.drawable.ic_http_24dp
                "HYSTERIA2" -> R.drawable.ic_hysteria_24dp
                "WIREGUARD" -> R.drawable.ic_wireguard_24dp
                else -> R.drawable.ic_server_24dp
            }
            
            binding.ivServerTypeIcon.setImageResource(iconRes)
        }
        
        private fun toggleServerDetails() {
            binding.apply {
                val isExpanded = layoutServerDetails.visibility == android.view.View.VISIBLE
                
                if (isExpanded) {
                    layoutServerDetails.visibility = android.view.View.GONE
                    ivExpandIcon.rotation = 0f
                } else {
                    layoutServerDetails.visibility = android.view.View.VISIBLE
                    ivExpandIcon.rotation = 180f
                }
            }
        }
    }
}
