package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ItemServerManagementBinding
import com.mohamedrady.v2hoor.dto.EConfigType
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.handler.AngConfigManager
import com.mohamedrady.v2hoor.handler.MmkvManager
import com.mohamedrady.v2hoor.util.Utils

class ServersManagementAdapter(private val activity: ServersManagementActivity) :
    RecyclerView.Adapter<ServersManagementAdapter.ServerViewHolder>() {

    private var serverGuids = mutableListOf<String>()
    private val shareOptions = arrayOf("QR Code", "Copy to Clipboard", "Share Full Config")

    fun updateServers(newServerGuids: List<String>) {
        serverGuids.clear()
        serverGuids.addAll(newServerGuids)
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ServerViewHolder {
        val binding = ItemServerManagementBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return ServerViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: ServerViewHolder, position: Int) {
        val guid = serverGuids[position]
        val profile = MmkvManager.decodeServerConfig(guid) ?: return

        holder.bind(guid, position)

        // Set server info
        holder.binding.tvServerName.text = profile.remarks.ifEmpty { "Server ${position + 1}" }
        holder.binding.tvServerAddress.text = getServerAddress(profile)
        holder.binding.tvServerType.text = profile.configType.name

        // Set server type icon and color
        setServerTypeIcon(holder, profile.configType)

        // Set connection status
        val isSelected = guid == MmkvManager.getSelectServer()
        setConnectionStatus(holder, isSelected)

        // Set ping status
        setPingStatus(holder, guid)

        // Click listeners
        holder.binding.cardServer.setOnClickListener {
            selectServer(guid)
        }

        holder.binding.btnEdit.setOnClickListener {
            editServer(guid, profile.configType)
        }

        holder.binding.btnShare.setOnClickListener {
            shareServer(guid, position)
        }

        holder.binding.btnDelete.setOnClickListener {
            deleteServer(guid, position)
        }

        holder.binding.btnTest.setOnClickListener {
            testServer(guid, position)
        }
    }

    override fun getItemCount(): Int = serverGuids.size
    
    private fun getServerAddress(profile: com.mohamedrady.v2hoor.dto.ProfileItem): String {
        return "${profile.server ?: ""}:${profile.serverPort ?: ""}"
    }
    
    private fun setServerTypeIcon(holder: ServerViewHolder, configType: EConfigType) {
        val (iconRes, colorRes) = when (configType) {
            EConfigType.VMESS -> R.drawable.ic_vmess to R.color.color_vmess
            EConfigType.VLESS -> R.drawable.ic_vless to R.color.color_vless
            EConfigType.SHADOWSOCKS -> R.drawable.ic_shadowsocks to R.color.color_shadowsocks
            EConfigType.TROJAN -> R.drawable.ic_trojan to R.color.color_trojan
            EConfigType.WIREGUARD -> R.drawable.ic_wireguard to R.color.color_wireguard
            EConfigType.SOCKS -> R.drawable.ic_socks to R.color.color_socks
            EConfigType.HTTP -> R.drawable.ic_http to R.color.color_http
            else -> R.drawable.ic_server to R.color.colorAccent
        }

        holder.binding.ivServerType.setImageResource(iconRes)
        holder.binding.ivServerType.setColorFilter(ContextCompat.getColor(activity, colorRes))
        holder.binding.tvServerType.setTextColor(ContextCompat.getColor(activity, colorRes))
        
        holder.binding.ivServerType.setImageResource(iconRes)
        holder.binding.ivServerType.setColorFilter(
            ContextCompat.getColor(activity, colorRes)
        )
    }
    
    private fun setConnectionStatus(holder: ServerViewHolder, isSelected: Boolean) {
        if (isSelected) {
            holder.binding.cardServer.setCardBackgroundColor(
                ContextCompat.getColor(activity, R.color.color_selected_server)
            )
            holder.binding.ivConnectionStatus.visibility = View.VISIBLE
            holder.binding.ivConnectionStatus.setImageResource(R.drawable.ic_check_circle)
            holder.binding.ivConnectionStatus.setColorFilter(
                ContextCompat.getColor(activity, R.color.color_connected)
            )
        } else {
            holder.binding.cardServer.setCardBackgroundColor(
                ContextCompat.getColor(activity, R.color.color_card_background)
            )
            holder.binding.ivConnectionStatus.visibility = View.GONE
        }
    }
    
    private fun setPingStatus(holder: ServerViewHolder, guid: String) {
        val aff = MmkvManager.decodeServerAffiliationInfo(guid)
        val delayMillis = aff?.testDelayMillis ?: 0L
        when {
            delayMillis == 0L -> {
                holder.binding.tvPing.text = activity.getString(R.string.connection_test_pending)
                holder.binding.tvPing.setTextColor(ContextCompat.getColor(activity, R.color.color_text_secondary))
            }
            delayMillis > 0 -> {
                holder.binding.tvPing.text = "${delayMillis}ms"
                val color = when {
                    delayMillis < 200 -> R.color.color_ping_excellent
                    delayMillis < 500 -> R.color.color_ping_good
                    delayMillis < 1000 -> R.color.color_ping_fair
                    else -> R.color.color_ping_poor
                }
                holder.binding.tvPing.setTextColor(ContextCompat.getColor(activity, color))
            }
            else -> {
                holder.binding.tvPing.text = activity.getString(R.string.connection_test_fail)
                holder.binding.tvPing.setTextColor(ContextCompat.getColor(activity, R.color.color_ping_fail))
            }
        }
    }
    
    private fun selectServer(guid: String) {
        MmkvManager.setSelectServer(guid)
        notifyDataSetChanged()
        activity.toast(activity.getString(R.string.toast_server_selected))
    }
    
    private fun editServer(guid: String, configType: EConfigType) {
        val intent = when (configType) {
            EConfigType.CUSTOM -> Intent(activity, ServerCustomConfigActivity::class.java)
            else -> Intent(activity, ServerActivity::class.java)
        }
        intent.putExtra("guid", guid)
        activity.startActivity(intent)
    }
    
    private fun shareServer(guid: String, position: Int) {
        AlertDialog.Builder(activity)
            .setTitle(activity.getString(R.string.share_server))
            .setItems(shareOptions) { _, which ->
                when (which) {
                    0 -> showQRCode(guid)
                    1 -> copyToClipboard(guid)
                    2 -> shareFullConfig(guid)
                }
            }
            .show()
    }
    
    private fun showQRCode(guid: String) {
        val config = MmkvManager.decodeServerConfig(guid)
        if (config != null) {
            // Create a simple share content from config
            val shareContent = "${config.configType.name}://${config.server}:${config.serverPort}"
            Utils.openUri(activity, shareContent)
        }
    }

    private fun copyToClipboard(guid: String) {
        val config = MmkvManager.decodeServerConfig(guid)
        if (config != null) {
            // Create a simple share content from config
            val shareContent = "${config.configType.name}://${config.server}:${config.serverPort}"
            Utils.setClipboard(activity, shareContent)
            activity.toast(activity.getString(R.string.toast_success))
        }
    }

    private fun shareFullConfig(guid: String) {
        val config = MmkvManager.decodeServerConfig(guid)
        if (config != null) {
            // Create a simple share content from config
            val shareContent = "${config.configType.name}://${config.server}:${config.serverPort}"
            Utils.setClipboard(activity, shareContent)
            activity.toast(activity.getString(R.string.toast_success))
        }
    }
    
    private fun deleteServer(guid: String, position: Int) {
        AlertDialog.Builder(activity)
            .setTitle(activity.getString(R.string.del_config_comfirm))
            .setMessage(activity.getString(R.string.del_config_comfirm_content))
            .setPositiveButton(android.R.string.ok) { _, _ ->
                MmkvManager.removeServer(guid)
                serverGuids.removeAt(position)
                notifyItemRemoved(position)
                notifyItemRangeChanged(position, serverGuids.size)
                activity.toast(activity.getString(R.string.toast_success))
            }
            .setNegativeButton(android.R.string.cancel, null)
            .show()
    }

    private fun testServer(guid: String, position: Int) {
        // Test server connection
        activity.toast(activity.getString(R.string.connection_test_testing))
        // Implement server testing logic here
    }
    
    class ServerViewHolder(val binding: ItemServerManagementBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(guid: String, position: Int) {
            // Bind data to views
        }
    }
}
