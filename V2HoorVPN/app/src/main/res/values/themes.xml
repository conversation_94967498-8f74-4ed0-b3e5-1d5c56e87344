<resources>

    <style name="AppThemeDayNight" parent="Theme.MaterialComponents.DayNight">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimary</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:statusBarColor">@color/colorPrimary</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="colorControlNormal">@color/colorAccent</item>
    </style>

    <style name="AppThemeDayNight.NoActionBar" parent="AppThemeDayNight">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.DarkActionBar" />

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>

    <style name="AppTheme.NoActionBar.Translucent">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="BrandedSwitch" parent="AppTheme">
        <item name="colorAccent">@color/color_fab_active</item>
    </style>

    <style name="TabLayoutTextStyle" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.MaterialComponents.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.MaterialComponents.Light" />
</resources>
