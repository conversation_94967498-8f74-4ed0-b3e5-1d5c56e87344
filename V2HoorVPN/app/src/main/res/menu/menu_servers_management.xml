<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <item
        android:id="@+id/action_search"
        android:icon="@drawable/ic_search_24dp"
        android:title="@string/search"
        app:actionViewClass="androidx.appcompat.widget.SearchView"
        app:showAsAction="ifRoom|collapseActionView" />

    <item
        android:id="@+id/action_test_all"
        android:icon="@drawable/ic_speed_test"
        android:title="@string/connection_test_testing_all"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_sort"
        android:icon="@drawable/ic_sort_24dp"
        android:title="@string/sort_servers"
        app:showAsAction="never">
        
        <menu>
            <item
                android:id="@+id/action_sort_by_name"
                android:title="@string/sort_by_name" />
            <item
                android:id="@+id/action_sort_by_ping"
                android:title="@string/sort_by_ping" />
            <item
                android:id="@+id/action_sort_by_type"
                android:title="@string/sort_by_type" />
            <item
                android:id="@+id/action_sort_by_date"
                android:title="@string/sort_by_date" />
        </menu>
        
    </item>

    <item
        android:id="@+id/action_import"
        android:icon="@drawable/ic_import_24dp"
        android:title="@string/import_servers"
        app:showAsAction="never">
        
        <menu>
            <item
                android:id="@+id/action_import_clipboard"
                android:icon="@drawable/ic_clipboard_24dp"
                android:title="@string/import_from_clipboard" />
            <item
                android:id="@+id/action_import_qr"
                android:icon="@drawable/ic_qr_code_24dp"
                android:title="@string/import_from_qr_code" />
            <item
                android:id="@+id/action_import_file"
                android:icon="@drawable/ic_file_24dp"
                android:title="@string/import_from_file" />
            <item
                android:id="@+id/action_import_subscription"
                android:icon="@drawable/ic_subscription_24dp"
                android:title="@string/import_from_subscription" />
        </menu>
        
    </item>

    <item
        android:id="@+id/action_export_all"
        android:icon="@drawable/ic_export_24dp"
        android:title="@string/export_all_servers"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_backup"
        android:icon="@drawable/ic_backup_24dp"
        android:title="@string/backup_servers"
        app:showAsAction="never">
        
        <menu>
            <item
                android:id="@+id/action_backup_export"
                android:title="@string/export_backup" />
            <item
                android:id="@+id/action_backup_import"
                android:title="@string/import_backup" />
        </menu>
        
    </item>

    <item
        android:id="@+id/action_manage"
        android:icon="@drawable/ic_manage_24dp"
        android:title="@string/manage_servers"
        app:showAsAction="never">
        
        <menu>
            <item
                android:id="@+id/action_select_all"
                android:title="@string/select_all" />
            <item
                android:id="@+id/action_delete_selected"
                android:title="@string/delete_selected" />
            <item
                android:id="@+id/action_delete_all"
                android:title="@string/del_all_config" />
            <item
                android:id="@+id/action_delete_duplicates"
                android:title="@string/del_duplicate_config" />
            <item
                android:id="@+id/action_delete_invalid"
                android:title="@string/del_invalid_config" />
        </menu>
        
    </item>

    <item
        android:id="@+id/action_settings"
        android:icon="@drawable/ic_settings_24dp"
        android:title="@string/settings"
        app:showAsAction="never" />

</menu>
