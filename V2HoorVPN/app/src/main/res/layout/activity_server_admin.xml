<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_background"
    tools:context=".ui.ServerAdminActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay"
            app:title="@string/server_admin_title"
            app:titleTextColor="@android:color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/padding_spacing_dp16">

                <!-- Server Statistics Card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/padding_spacing_dp16"
                    app:cardCornerRadius="@dimen/card_corner_radius"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="@dimen/padding_spacing_dp16">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/server_statistics"
                            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                            android:textColor="@color/color_text_primary"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_server_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/padding_spacing_dp8"
                            android:text="عدد السيرفرات: 0"
                            android:textAppearance="@style/TextAppearance.AppCompat.Small"
                            android:textColor="@color/color_text_secondary" />

                        <!-- Sync Buttons Row 1 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/padding_spacing_dp16"
                            android:orientation="horizontal">

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_sync_to_local"
                                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/padding_spacing_dp4"
                                android:layout_weight="1"
                                android:text="@string/sync_to_local"
                                android:textSize="9sp"
                                app:icon="@drawable/ic_download_24dp"
                                app:iconSize="12dp" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_sync_to_firebase"
                                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/padding_spacing_dp4"
                                android:layout_weight="1"
                                android:text="@string/sync_to_firebase"
                                android:textSize="9sp"
                                app:icon="@drawable/ic_upload_24dp"
                                app:iconSize="12dp" />

                        </LinearLayout>

                        <!-- Test Buttons Row 2 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/padding_spacing_dp8"
                            android:orientation="horizontal">

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_test_connection"
                                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/padding_spacing_dp4"
                                android:layout_weight="1"
                                android:text="@string/test_connection"
                                android:textSize="9sp"
                                app:icon="@drawable/ic_test_24dp"
                                app:iconSize="12dp" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_add_sample_data"
                                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/padding_spacing_dp4"
                                android:layout_weight="1"
                                android:text="@string/add_sample_data"
                                android:textSize="9sp"
                                app:icon="@drawable/ic_add_24dp"
                                app:iconSize="12dp" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Category Filter -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/padding_spacing_dp16"
                    app:cardCornerRadius="@dimen/card_corner_radius"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="@dimen/padding_spacing_dp16">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/filter_by_category"
                            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                            android:textColor="@color/color_text_primary"
                            android:textStyle="bold" />

                        <com.google.android.material.chip.ChipGroup
                            android:id="@+id/chip_group_categories"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/padding_spacing_dp8"
                            app:chipSpacingHorizontal="@dimen/padding_spacing_dp8"
                            app:singleSelection="true" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Progress Bar -->
                <ProgressBar
                    android:id="@+id/progress_bar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginBottom="@dimen/padding_spacing_dp16"
                    android:visibility="gone" />

                <!-- Servers List -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:nestedScrollingEnabled="false"
                    android:paddingBottom="@dimen/padding_spacing_dp80"
                    tools:listitem="@layout/item_server_admin" />

                <!-- Empty State -->
                <LinearLayout
                    android:id="@+id/layout_empty_state"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/padding_spacing_dp32"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="@dimen/empty_state_icon_size"
                        android:layout_height="@dimen/empty_state_icon_size"
                        android:alpha="0.5"
                        android:src="@drawable/ic_server_empty"
                        android:tint="@color/color_text_secondary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/padding_spacing_dp16"
                        android:text="@string/no_servers_found"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textColor="@color/color_text_secondary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/padding_spacing_dp8"
                        android:text="@string/add_server_to_get_started"
                        android:textAppearance="@style/TextAppearance.AppCompat.Small"
                        android:textColor="@color/color_text_hint" />

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_server"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="@dimen/padding_spacing_dp16"
        android:clickable="true"
        android:focusable="true"
        android:src="@drawable/ic_add_24dp"
        app:backgroundTint="@color/colorAccent"
        app:elevation="6dp"
        app:fabSize="normal"
        app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
