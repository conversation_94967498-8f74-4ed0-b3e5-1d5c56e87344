<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="4dp"
        app:elevation="4dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!-- Progress Indicator -->
            <com.google.android.material.progressindicator.LinearProgressIndicator
                android:id="@+id/pb_waiting"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:indeterminate="true"
                android:visibility="gone"
                app:indicatorColor="@color/color_fab_active"
                app:trackColor="@color/color_progress_track" />

            <!-- Search Bar -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/padding_spacing_dp8"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="2dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/padding_spacing_dp4"
                    app:boxBackgroundMode="none"
                    app:hintEnabled="false"
                    app:startIconDrawable="@drawable/ic_search_24dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_search"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/search_servers"
                        android:imeOptions="actionSearch"
                        android:inputType="text"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Server Type Tabs -->
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/color_tab_background"
                android:padding="@dimen/padding_spacing_dp4"
                app:tabIndicatorColor="@color/colorAccent"
                app:tabIndicatorFullWidth="false"
                app:tabMode="scrollable"
                app:tabSelectedTextColor="@color/colorAccent"
                app:tabTextAppearance="@style/TabLayoutTextStyle"
                app:tabTextColor="@color/color_text_secondary" />

            <!-- Servers List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:clipToPadding="false"
                android:paddingTop="@dimen/padding_spacing_dp4"
                android:paddingBottom="@dimen/padding_spacing_dp80"
                android:scrollbars="vertical" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/layout_empty_state"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="@dimen/empty_state_icon_size"
                    android:layout_height="@dimen/empty_state_icon_size"
                    android:alpha="0.5"
                    android:src="@drawable/ic_server_empty"
                    android:tint="@color/color_text_secondary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/padding_spacing_dp16"
                    android:text="@string/no_servers_found"
                    android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                    android:textColor="@color/color_text_secondary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/padding_spacing_dp8"
                    android:text="@string/add_server_to_get_started"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textColor="@color/color_text_hint" />

            </LinearLayout>

        </LinearLayout>

        <!-- Floating Action Button -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_margin="@dimen/padding_spacing_dp16"
            android:clickable="true"
            android:focusable="true"
            android:src="@drawable/ic_add_24dp"
            app:backgroundTint="@color/colorAccent"
            app:elevation="6dp"
            app:fabSize="normal"
            app:tint="@android:color/white" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</LinearLayout>

