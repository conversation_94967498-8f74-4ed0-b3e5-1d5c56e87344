<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_server"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/padding_spacing_dp8"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="@dimen/card_corner_radius"
    app:cardElevation="2dp"
    app:rippleColor="@color/colorAccent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/padding_spacing_dp16">

        <!-- Server Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- Server Type Icon -->
            <ImageView
                android:id="@+id/iv_server_type_icon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="@dimen/padding_spacing_dp12"
                android:src="@drawable/ic_server_24dp"
                android:tint="@color/colorAccent" />

            <!-- Server Info -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_server_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                    android:textColor="@color/color_text_primary"
                    android:textStyle="bold"
                    tools:text="سيرفر أمريكا" />

                <TextView
                    android:id="@+id/tv_server_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textColor="@color/color_text_secondary"
                    tools:text="***********:8080" />

            </LinearLayout>

            <!-- Status and Type Chips -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_status"
                    style="@style/Widget.MaterialComponents.Chip.Action"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="نشط"
                    android:textSize="10sp" />

                <TextView
                    android:id="@+id/tv_server_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:background="@drawable/bg_server_type"
                    android:paddingHorizontal="@dimen/padding_spacing_dp8"
                    android:paddingVertical="@dimen/padding_spacing_dp4"
                    android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                    android:textColor="@android:color/white"
                    android:textSize="10sp"
                    tools:text="VMESS" />

            </LinearLayout>

            <!-- Expand Icon -->
            <ImageView
                android:id="@+id/iv_expand_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="@dimen/padding_spacing_dp8"
                android:src="@drawable/ic_expand_more_24dp"
                android:tint="@color/color_text_secondary" />

        </LinearLayout>

        <!-- Server Quick Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp12"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_server_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_category_chip"
                android:paddingHorizontal="@dimen/padding_spacing_dp8"
                android:paddingVertical="@dimen/padding_spacing_dp4"
                android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                android:textColor="@color/colorAccent"
                android:textSize="10sp"
                tools:text="افتراضي" />

            <TextView
                android:id="@+id/tv_ping"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/padding_spacing_dp8"
                android:background="@drawable/bg_ping_chip"
                android:paddingHorizontal="@dimen/padding_spacing_dp8"
                android:paddingVertical="@dimen/padding_spacing_dp4"
                android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                android:textColor="@color/color_success"
                android:textSize="10sp"
                android:visibility="gone"
                tools:text="50ms"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_speed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/padding_spacing_dp8"
                android:background="@drawable/bg_speed_chip"
                android:paddingHorizontal="@dimen/padding_spacing_dp8"
                android:paddingVertical="@dimen/padding_spacing_dp4"
                android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                android:textColor="@color/color_info"
                android:textSize="10sp"
                android:visibility="gone"
                tools:text="100MB/s"
                tools:visibility="visible" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_read_only"
                style="@style/Widget.MaterialComponents.Chip.Action"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/padding_spacing_dp8"
                android:text="للقراءة فقط"
                android:textSize="10sp"
                android:visibility="gone"
                app:chipBackgroundColor="@color/color_warning" />

        </LinearLayout>

        <!-- Server Details (Expandable) -->
        <LinearLayout
            android:id="@+id/layout_server_details"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp16"
            android:orientation="vertical"
            android:visibility="gone">

            <!-- Description -->
            <TextView
                android:id="@+id/tv_server_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/padding_spacing_dp8"
                android:textAppearance="@style/TextAppearance.AppCompat.Small"
                android:textColor="@color/color_text_secondary"
                tools:text="وصف السيرفر هنا" />

            <!-- Server Location and Provider -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/padding_spacing_dp8"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="الدولة: "
                    android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                    android:textColor="@color/color_text_secondary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_server_country"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                    android:textColor="@color/color_text_secondary"
                    tools:text="الولايات المتحدة" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/padding_spacing_dp16"
                    android:text="المزود: "
                    android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                    android:textColor="@color/color_text_secondary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_server_provider"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                    android:textColor="@color/color_text_secondary"
                    tools:text="CloudFlare" />

            </LinearLayout>

            <!-- Timestamps -->
            <TextView
                android:id="@+id/tv_created_at"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="2dp"
                android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                android:textColor="@color/color_text_hint"
                tools:text="تم الإنشاء: 01/01/2024 12:00" />

            <TextView
                android:id="@+id/tv_updated_at"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/padding_spacing_dp12"
                android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                android:textColor="@color/color_text_hint"
                tools:text="آخر تحديث: 01/01/2024 12:00" />

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_edit"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/padding_spacing_dp8"
                    android:layout_weight="1"
                    android:text="@string/edit"
                    android:textSize="12sp"
                    app:icon="@drawable/ic_edit_24dp"
                    app:iconSize="16dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_delete"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/padding_spacing_dp8"
                    android:layout_weight="1"
                    android:text="@string/delete"
                    android:textColor="@color/color_error"
                    android:textSize="12sp"
                    app:icon="@drawable/ic_delete_24dp"
                    app:iconSize="16dp"
                    app:strokeColor="@color/color_error" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
