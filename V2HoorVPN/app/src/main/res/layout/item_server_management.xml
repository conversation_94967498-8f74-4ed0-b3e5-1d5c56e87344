<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_server"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/padding_spacing_dp8"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="@dimen/card_corner_radius"
    app:cardElevation="2dp"
    app:strokeColor="@color/color_card_stroke"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/padding_spacing_dp16">

        <!-- Server Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- Server Type Icon -->
            <ImageView
                android:id="@+id/iv_server_type"
                android:layout_width="@dimen/server_type_icon_size"
                android:layout_height="@dimen/server_type_icon_size"
                android:layout_marginEnd="@dimen/padding_spacing_dp12"
                android:contentDescription="@string/server_type"
                android:src="@drawable/ic_server"
                android:tint="@color/colorAccent" />

            <!-- Server Info -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <!-- Server Name -->
                <TextView
                    android:id="@+id/tv_server_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                    android:textColor="@color/color_text_primary"
                    android:textStyle="bold"
                    tools:text="My VPN Server" />

                <!-- Server Address -->
                <TextView
                    android:id="@+id/tv_server_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/padding_spacing_dp2"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textColor="@color/color_text_secondary"
                    tools:text="example.com:443" />

            </LinearLayout>

            <!-- Connection Status -->
            <ImageView
                android:id="@+id/iv_connection_status"
                android:layout_width="@dimen/connection_status_icon_size"
                android:layout_height="@dimen/connection_status_icon_size"
                android:layout_marginStart="@dimen/padding_spacing_dp8"
                android:contentDescription="@string/connection_status"
                android:src="@drawable/ic_check_circle"
                android:tint="@color/color_connected"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Server Details -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp12"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- Server Type Badge -->
            <com.google.android.material.chip.Chip
                android:id="@+id/tv_server_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/padding_spacing_dp8"
                android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                android:textColor="@color/color_chip_text"
                app:chipBackgroundColor="@color/color_chip_background"
                app:chipCornerRadius="@dimen/chip_corner_radius"
                app:chipMinHeight="@dimen/chip_min_height"
                app:chipStrokeColor="@color/color_chip_stroke"
                app:chipStrokeWidth="1dp"
                tools:text="VMESS" />

            <!-- Spacer -->
            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <!-- Ping Status -->
            <TextView
                android:id="@+id/tv_ping"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/padding_spacing_dp8"
                android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                android:textColor="@color/color_text_secondary"
                android:textStyle="bold"
                tools:text="120ms" />

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp12"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- Test Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_test"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="@dimen/padding_spacing_dp4"
                android:text="@string/connection_test_testing"
                android:textColor="@color/colorAccent"
                android:textSize="12sp"
                app:icon="@drawable/ic_speed_test"
                app:iconGravity="textStart"
                app:iconSize="16dp"
                app:iconTint="@color/colorAccent" />

            <!-- Edit Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_edit"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/padding_spacing_dp4"
                android:layout_marginEnd="@dimen/padding_spacing_dp4"
                android:text="@string/edit"
                android:textColor="@color/color_edit"
                android:textSize="12sp"
                app:icon="@drawable/ic_edit_24dp"
                app:iconGravity="textStart"
                app:iconSize="16dp"
                app:iconTint="@color/color_edit" />

            <!-- Share Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_share"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/padding_spacing_dp4"
                android:layout_marginEnd="@dimen/padding_spacing_dp4"
                android:text="@string/share"
                android:textColor="@color/color_share"
                android:textSize="12sp"
                app:icon="@drawable/ic_share_24dp"
                app:iconGravity="textStart"
                app:iconSize="16dp"
                app:iconTint="@color/color_share" />

            <!-- Delete Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_delete"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/padding_spacing_dp4"
                android:text="@string/delete"
                android:textColor="@color/color_delete"
                android:textSize="12sp"
                app:icon="@drawable/ic_delete_24dp"
                app:iconGravity="textStart"
                app:iconSize="16dp"
                app:iconTint="@color/color_delete" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
